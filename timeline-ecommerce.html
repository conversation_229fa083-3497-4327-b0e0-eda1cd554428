<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historia del E-commerce - Línea de Tiempo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 15px 10px;
            min-height: 100vh;
            max-height: 100vh;
            overflow: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            flex-shrink: 0;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2rem;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .timeline {
            position: relative;
            padding: 10px 0;
            flex: 1;
            overflow-y: auto;
            max-height: calc(100vh - 120px);
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #3498db, #2980b9);
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .timeline-item {
            position: relative;
            margin: 8px 0;
            width: 100%;
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 0;
            margin-right: 52%;
            text-align: right;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-left: 52%;
            margin-right: 0;
            text-align: left;
        }

        .timeline-content {
            background: linear-gradient(135deg, #87ceeb 0%, #ffffff 100%);
            padding: 10px 12px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #87ceeb;
            position: relative;
            transition: all 0.3s ease;
            max-width: 280px;
        }

        .timeline-content:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .timeline-item:nth-child(odd) .timeline-content::after {
            content: '';
            position: absolute;
            right: -12px;
            top: 20px;
            width: 0;
            height: 0;
            border-left: 12px solid #87ceeb;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .timeline-item:nth-child(even) .timeline-content::after {
            content: '';
            position: absolute;
            left: -12px;
            top: 20px;
            width: 0;
            height: 0;
            border-right: 12px solid #87ceeb;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .timeline-year {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            background: #2c3e50;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.85rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            z-index: 10;
            top: 10px;
        }

        .milestone-title {
            color: #2c3e50;
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .milestone-description {
            color: #34495e;
            font-size: 0.8rem;
            line-height: 1.4;
            text-align: justify;
        }

        /* Scrollbar personalizado para la timeline */
        .timeline::-webkit-scrollbar {
            width: 6px;
        }

        .timeline::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .timeline::-webkit-scrollbar-thumb {
            background: #87ceeb;
            border-radius: 3px;
        }

        .timeline::-webkit-scrollbar-thumb:hover {
            background: #5dade2;
        }

        @media (max-width: 768px) {
            .timeline::before {
                left: 30px;
            }

            .timeline-item:nth-child(odd) .timeline-content,
            .timeline-item:nth-child(even) .timeline-content {
                margin-left: 60px;
                margin-right: 10px;
                text-align: left;
                max-width: 280px;
            }

            .timeline-item:nth-child(odd) .timeline-content::after,
            .timeline-item:nth-child(even) .timeline-content::after {
                left: -12px;
                right: auto;
                border-left: none;
                border-right: 12px solid #87ceeb;
            }

            .timeline-year {
                left: 30px;
                transform: translateX(-50%);
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .milestone-title {
                font-size: 0.9rem;
            }

            .milestone-description {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Historia del E-commerce</h1>
            <p>Evolución del comercio electrónico desde sus inicios hasta la actualidad</p>
        </div>

        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-year">1990</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Nace la Web</h3>
                    <p class="milestone-description">Nace la Web y las compras online.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">1994</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Primera Transacción Segura</h3>
                    <p class="milestone-description">Primera transacción segura con SSL.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">1995</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Amazon y eBay</h3>
                    <p class="milestone-description">Surgen Amazon y eBay.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">1998</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">PayPal</h3>
                    <p class="milestone-description">Aparece PayPal.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">1999</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">$150 Mil Millones</h3>
                    <p class="milestone-description">eCommerce global llega a $150 mil millones.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">2000</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Burbuja Puntocom</h3>
                    <p class="milestone-description">Burbuja puntocom y nace Google AdWords.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">2005</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Amazon Prime</h3>
                    <p class="milestone-description">Amazon Prime y Cyber Monday impulsan el sector.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">2006</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Shopify</h3>
                    <p class="milestone-description">Shopify y otras plataformas facilitan tiendas online.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">2012</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">Instacart</h3>
                    <p class="milestone-description">Instacart digitaliza comestibles y mejora logística.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">2017</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">$29,267 Billones</h3>
                    <p class="milestone-description">eCommerce alcanza $29,267 billones, domina B2B.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-year">2020</div>
                <div class="timeline-content">
                    <h3 class="milestone-title">COVID-19</h3>
                    <p class="milestone-description">COVID-19 acelera la adopción digital.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
